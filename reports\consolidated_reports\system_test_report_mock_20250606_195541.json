{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-06T19:55:41.245537", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0, "success_rate": 0.0, "total_execution_time": 8.582091808319092, "start_time": "2025-06-06T19:55:32.662456", "end_time": "2025-06-06T19:55:41.244548"}, "sequence_results": [{"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 1.1587750911712646, "start_time": "2025-06-06T19:55:32.663340", "end_time": "2025-06-06T19:55:33.822114", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 458, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9456257820129395, "start_time": "2025-06-06T19:55:33.823005", "end_time": "2025-06-06T19:55:34.768632", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 56, in <module>\n    from TXDLib.Handlers import audio_processing as ap\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\audio_processing.py\", line 43, in <module>\n    import niscope                     #routines for the NI PXI Scope\n    ^^^^^^^^^^^^^^\nModuleNotFoundError: No module named 'niscope'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9214730262756348, "start_time": "2025-06-06T19:55:34.769806", "end_time": "2025-06-06T19:55:35.691280", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 127, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 91, in main\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9502971172332764, "start_time": "2025-06-06T19:55:35.692092", "end_time": "2025-06-06T19:55:36.642390", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 48, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\D3054Scope.py\", line 28, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9300627708435059, "start_time": "2025-06-06T19:55:36.643688", "end_time": "2025-06-06T19:55:37.573751", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 39, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\D3054Scope.py\", line 28, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9333560466766357, "start_time": "2025-06-06T19:55:37.574488", "end_time": "2025-06-06T19:55:38.507845", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 36, in <module>\n    from TXDLib.Handlers import N9010BSpecAn\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\N9010BSpecAn.py\", line 24, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9487144947052002, "start_time": "2025-06-06T19:55:38.509170", "end_time": "2025-06-06T19:55:39.457885", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 54, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.8939721584320068, "start_time": "2025-06-06T19:55:39.458658", "end_time": "2025-06-06T19:55:40.352632", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 247, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.8900694847106934, "start_time": "2025-06-06T19:55:40.353809", "end_time": "2025-06-06T19:55:41.243879", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 84, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 8.582091808319092, "average_sequence_time": 0.953565756479899, "sequences_per_hour": 3775.3033553652863, "optimization_effectiveness": {"optimization_success_rate": 0.0, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "NEEDS_REVIEW"}}, "failure_analysis": {"total_failures": 9, "failure_by_procedure": {"DO189": 9}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 9 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}