"""
Mock matplotlib module for TXD testing
"""

class MockPyplot:
    """Mock matplotlib.pyplot"""
    
    def figure(self, *args, **kwargs):
        return MockFigure()
    
    def plot(self, *args, **kwargs):
        pass
    
    def xlabel(self, *args, **kwargs):
        pass
    
    def ylabel(self, *args, **kwargs):
        pass
    
    def title(self, *args, **kwargs):
        pass
    
    def grid(self, *args, **kwargs):
        pass
    
    def show(self, *args, **kwargs):
        pass
    
    def savefig(self, *args, **kwargs):
        pass
    
    def close(self, *args, **kwargs):
        pass
    
    def clf(self):
        pass
    
    def cla(self):
        pass

class MockFigure:
    """Mock matplotlib figure"""
    
    def add_subplot(self, *args, **kwargs):
        return MockAxes()
    
    def savefig(self, *args, **kwargs):
        pass

class MockAxes:
    """Mock matplotlib axes"""
    
    def plot(self, *args, **kwargs):
        pass
    
    def set_xlabel(self, *args, **kwargs):
        pass
    
    def set_ylabel(self, *args, **kwargs):
        pass
    
    def set_title(self, *args, **kwargs):
        pass
    
    def grid(self, *args, **kwargs):
        pass

# Create pyplot instance
pyplot = MockPyplot()
