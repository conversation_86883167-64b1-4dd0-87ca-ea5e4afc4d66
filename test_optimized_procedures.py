#!/usr/bin/env python3
"""
Direct test of optimized procedures with mock handlers
"""

import sys
import os
import time
from pathlib import Path

# Add MockHandlers to Python path to override TXDLib.Handlers
mock_handlers_path = str(Path.cwd() / "MockHandlers")
if mock_handlers_path not in sys.path:
    sys.path.insert(0, mock_handlers_path)

# Set environment variables for mock mode
os.environ['TXD_EXECUTION_MODE'] = 'MOCK'
os.environ['TXD_MOCK_MODE'] = '1'

def test_do189_2_2_3():
    """Test the optimized DO_189_2_2_3.py procedure"""
    print("=" * 70)
    print("TESTING: DO189/DO_189_2_2_3.py (DME Pulse Measurements)")
    print("=" * 70)
    
    start_time = time.time()
    
    try:
        # Import the optimized procedure
        sys.path.insert(0, str(Path.cwd() / "TXDLib" / "Procedures" / "DO189"))
        
        # Mock the required modules
        import MockHandlers.ate_rm as ate_rm_mock
        import MockHandlers.ATC5000NG as ATC5000NG_mock
        import MockHandlers.ARINC_Client as ARINC_Client_mock
        import MockHandlers.D3054Scope as D3054Scope_mock

        # Create mock instances
        rm = ate_rm_mock.ate_rm()
        atc = ATC5000NG_mock.ATC5000NG(rm)
        scope = D3054Scope_mock.D3054Scope()
        ARINC = ARINC_Client_mock.ARINC_Client(rm, "mock_server_path")
        
        # Import the optimized functions
        from DO_189_2_2_3 import (
            wait_for_arinc_ready,
            wait_for_atc_measurement_ready,
            wait_for_scope_ready,
            wait_for_measurement_complete,
            adaptive_retry_delay,
            init_DME_Standard
        )
        
        print("✅ Successfully imported optimized helper functions")
        
        # Test the optimization functions
        print("\nTesting optimization helper functions:")
        
        # Test ARINC ready polling
        print("  Testing wait_for_arinc_ready...")
        start = time.time()
        result = wait_for_arinc_ready(ARINC, timeout=5, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test ATC measurement ready polling
        print("  Testing wait_for_atc_measurement_ready...")
        start = time.time()
        result = wait_for_atc_measurement_ready(atc, timeout=3, poll_interval=0.2)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test scope ready polling
        print("  Testing wait_for_scope_ready...")
        start = time.time()
        result = wait_for_scope_ready(scope, timeout=2, poll_interval=0.1)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test measurement complete polling
        print("  Testing wait_for_measurement_complete...")
        start = time.time()
        result = wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test adaptive retry delay
        print("  Testing adaptive_retry_delay...")
        start = time.time()
        result = adaptive_retry_delay(timeout=1, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test init_DME_Standard function with optimization
        print("  Testing init_DME_Standard with optimization...")
        start = time.time()
        init_DME_Standard(atc, ARINC)
        duration = time.time() - start
        print(f"    Completed in: {duration:.2f}s (should be ~0.5-1s vs original 5s)")
        
        total_time = time.time() - start_time
        print(f"\n✅ DO_189_2_2_3.py test PASSED in {total_time:.2f}s")
        return True
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n❌ DO_189_2_2_3.py test FAILED in {total_time:.2f}s")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_do189_2_2_4():
    """Test the optimized DO_189_2_2_4.py procedure"""
    print("\n" + "=" * 70)
    print("TESTING: DO189/DO_189_2_2_4.py (Channel Spacing Measurements)")
    print("=" * 70)
    
    start_time = time.time()
    
    try:
        # Import the optimized procedure
        sys.path.insert(0, str(Path.cwd() / "TXDLib" / "Procedures" / "DO189"))
        
        # Mock the required modules
        import MockHandlers.ate_rm as ate_rm_mock
        import MockHandlers.ATC5000NG as ATC5000NG_mock
        import MockHandlers.ARINC_Client as ARINC_Client_mock

        # Create mock instances
        rm = ate_rm_mock.ate_rm()
        atc = ATC5000NG_mock.ATC5000NG(rm)
        ARINC = ARINC_Client_mock.ARINC_Client(rm, "mock_server_path")
        
        # Import the optimized functions
        from DO_189_2_2_4 import (
            wait_for_arinc_ready,
            wait_for_atc_measurement_ready,
            wait_for_measurement_complete,
            adaptive_retry_delay,
            init_DME_Standard,
            set_VOR_PAIR5
        )
        
        print("✅ Successfully imported optimized helper functions")
        
        # Test the optimization functions
        print("\nTesting optimization helper functions:")
        
        # Test init_DME_Standard function with optimization
        print("  Testing init_DME_Standard with optimization...")
        start = time.time()
        init_DME_Standard(0.5, atc, ARINC)  # cable_loss = 0.5
        duration = time.time() - start
        print(f"    Completed in: {duration:.2f}s (should be ~0.5-1s vs original 5s)")
        
        # Test set_VOR_PAIR5 function with optimization
        print("  Testing set_VOR_PAIR5 with optimization...")
        start = time.time()
        set_VOR_PAIR5(atc, ARINC)
        duration = time.time() - start
        print(f"    Completed in: {duration:.2f}s (should be ~1-2s vs original 16s)")
        
        total_time = time.time() - start_time
        print(f"\n✅ DO_189_2_2_4.py test PASSED in {total_time:.2f}s")
        return True
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n❌ DO_189_2_2_4.py test FAILED in {total_time:.2f}s")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_do181_2_3_2_3_2a():
    """Test the optimized DO_181E_2_3_2_3_2a.py procedure"""
    print("\n" + "=" * 70)
    print("TESTING: DO181/DO_181E_2_3_2_3_2a.py (Mode S Pulse Analysis)")
    print("=" * 70)
    
    start_time = time.time()
    
    try:
        # Import the optimized procedure
        sys.path.insert(0, str(Path.cwd() / "TXDLib" / "Procedures" / "DO181"))
        
        # Mock the required modules
        import MockHandlers.ate_rm as ate_rm_mock
        import MockHandlers.ATC5000NG as ATC5000NG_mock
        import MockHandlers.B4500CPwrMeter as B4500CPwrMeter_mock
        import MockHandlers.D3054Scope as D3054Scope_mock

        # Create mock instances
        rm = ate_rm_mock.ate_rm()
        atc = ATC5000NG_mock.ATC5000NG(rm)
        pwrmtr = B4500CPwrMeter_mock.B4500CPwrMeter()
        scope = D3054Scope_mock.D3054Scope()
        
        # Import the optimized functions
        from DO_181E_2_3_2_3_2a import (
            wait_for_power_meter_ready,
            wait_for_atc_measurement_ready,
            wait_for_rf_state_change,
            wait_for_measurement_complete,
            adaptive_retry_delay,
            pw_init,
            find_pulses
        )
        
        print("✅ Successfully imported optimized helper functions")
        
        # Test the optimization functions
        print("\nTesting optimization helper functions:")
        
        # Test power meter ready polling
        print("  Testing wait_for_power_meter_ready...")
        start = time.time()
        result = wait_for_power_meter_ready(pwrmtr, timeout=5, poll_interval=0.3)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test RF state change polling
        print("  Testing wait_for_rf_state_change...")
        start = time.time()
        result = wait_for_rf_state_change(atc, timeout=8, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s")
        
        # Test pw_init function with optimization
        print("  Testing pw_init with optimization...")
        start = time.time()
        pw_init(pwrmtr, 'ModeS')
        duration = time.time() - start
        print(f"    Completed in: {duration:.2f}s (should be ~0.3-0.5s vs original 5s)")
        
        # Test find_pulses function with optimization
        print("  Testing find_pulses with optimization...")
        start = time.time()
        pwr, nPulse = find_pulses(pwrmtr, "Test Power Meter")
        duration = time.time() - start
        print(f"    Completed in: {duration:.2f}s, Found {nPulse} pulses")
        
        total_time = time.time() - start_time
        print(f"\n✅ DO_181E_2_3_2_3_2a.py test PASSED in {total_time:.2f}s")
        return True
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n❌ DO_181E_2_3_2_3_2a.py test FAILED in {total_time:.2f}s")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test execution"""
    print("TXD QUALIFICATION TEST SYSTEM - OPTIMIZATION VALIDATION")
    print("=" * 70)
    print("Testing optimized sleep/delay procedures with mock interfaces")
    print("=" * 70)
    
    total_start_time = time.time()
    
    # Run all tests
    results = []
    results.append(test_do189_2_2_3())
    results.append(test_do189_2_2_4())
    results.append(test_do181_2_3_2_3_2a())
    
    total_time = time.time() - total_start_time
    
    # Summary
    print("\n" + "=" * 70)
    print("OPTIMIZATION VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    print(f"Total Execution Time: {total_time:.2f}s")
    
    if passed == total:
        print("\n✅ ALL OPTIMIZATION TESTS PASSED!")
        print("Sleep/delay optimizations are working correctly with mock interfaces.")
        return 0
    else:
        print(f"\n❌ {total - passed} OPTIMIZATION TESTS FAILED!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
