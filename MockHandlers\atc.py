"""
Mock atc module for direct imports
"""

class MockATC:
    """Mock ATC class"""
    
    def __init__(self):
        self.connected = False
        self.status = "Ready"
    
    def connect(self):
        self.connected = True
        return True
    
    def disconnect(self):
        self.connected = False
    
    def send_command(self, command):
        return "OK"
    
    def get_status(self):
        return self.status

# Create ATC class for import
ATC = MockATC
