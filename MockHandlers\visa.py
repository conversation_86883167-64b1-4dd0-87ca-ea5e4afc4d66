"""
Mock visa module for TXD testing
"""

class MockResource:
    """Mock VISA resource"""
    
    def __init__(self, resource_name):
        self.resource_name = resource_name
        self.timeout = 5000
        
    def write(self, command):
        return len(command)
    
    def read(self):
        return "OK"
    
    def query(self, command):
        if "IDN" in command.upper():
            return "Mock Instrument,Model123,SN456,FW1.0"
        return "0"
    
    def close(self):
        pass

class MockResourceManager:
    """Mock VISA resource manager"""
    
    def open_resource(self, resource_name, **kwargs):
        return MockResource(resource_name)
    
    def list_resources(self):
        return ['TCPIP::*************::INSTR']
    
    def close(self):
        pass

def ResourceManager():
    """Create mock resource manager"""
    return MockResourceManager()

# Constants
VI_SUCCESS = 0
VI_ERROR_TIMEOUT = -1073807339
