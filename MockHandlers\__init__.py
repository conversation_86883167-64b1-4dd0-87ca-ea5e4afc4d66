"""
TXD Qualification Test System - Mock Handlers
Contains mock implementations of all hardware interface handlers for testing without hardware dependencies
"""

__version__ = "1.0.0"
__author__ = "TXD Qualification Test System - Mock Implementation"

# Import all mock handlers to make them available
from . import ate_rm
from . import ATC5000NG
from . import ARINC_Client
from . import D3054Scope
from . import B4500CPwrMeter
from . import N9010BSpecAn
from . import N5172BSigGen
from . import RFBOB
from . import DigitalBOB
from . import audio_processing

# Import additional mock modules
from . import numpy
from . import matplotlib
from . import pyvisa
from . import niscope
from . import ftd2xx
from . import toml
from . import reedsolo
from . import visa
from . import atc

# Make handlers available at package level for direct import
__all__ = [
    'ate_rm',
    'ATC5000NG',
    'ARINC_Client',
    'D3054Scope',
    'B4500CPwrMeter',
    'N9010BSpecAn',
    'N5172BSigGen',
    'RFBOB',
    'DigitalBOB',
    'audio_processing',
    'numpy',
    'matplotlib',
    'pyvisa',
    'niscope',
    'ftd2xx',
    'toml',
    'reedsolo',
    'visa',
    'atc'
]
