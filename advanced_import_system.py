#!/usr/bin/env python3
"""
Advanced Import System for TXD Qualification Test System
Implements complete import redirection with dual interface support
"""

import sys
import os
import importlib
import importlib.util
from pathlib import Path
from types import ModuleType
from typing import Any, Dict, List, Optional

class TXDImportRedirector:
    """Advanced import redirector with dual interface support"""
    
    def __init__(self):
        self.original_import = __builtins__.__import__
        self.mock_modules = {}
        self.mock_handlers_path = str(Path.cwd() / "MockHandlers")
        self.setup_mock_environment()
        
    def setup_mock_environment(self):
        """Setup complete mock environment"""
        # Add MockHandlers to path
        if self.mock_handlers_path not in sys.path:
            sys.path.insert(0, self.mock_handlers_path)
        
        # Set environment variables
        os.environ['TXD_EXECUTION_MODE'] = 'MOCK'
        os.environ['TXD_MOCK_MODE'] = '1'
        
        # Install import hook
        __builtins__.__import__ = self.mock_import
        
    def create_dual_interface_module(self, module_name: str, mock_module: Any) -> ModuleType:
        """Create a module that supports both module access and function calls"""
        
        # Create a new module
        dual_module = ModuleType(module_name)
        
        # Copy all attributes from mock module
        if hasattr(mock_module, '__dict__'):
            for attr_name, attr_value in mock_module.__dict__.items():
                if not attr_name.startswith('_'):
                    setattr(dual_module, attr_name, attr_value)
        
        # Special handling for ate_rm - make it callable
        if 'ate_rm' in module_name:
            # Get the class from the mock module
            if hasattr(mock_module, 'ATE_RM'):
                ate_rm_class = mock_module.ATE_RM
            elif hasattr(mock_module, 'ate_rm'):
                ate_rm_class = mock_module.ate_rm
            else:
                # Create a minimal mock class
                class MockATERM:
                    def __init__(self):
                        self.instruments = {}
                    def addInstrument(self, name, inst):
                        self.instruments[name] = inst
                    def getInstrument(self, name):
                        return self.instruments.get(name)
                    def cleanup(self):
                        pass
                ate_rm_class = MockATERM
            
            # Make the module callable to return an instance
            def module_call(*args, **kwargs):
                return ate_rm_class(*args, **kwargs)
            
            # Add __call__ method to module
            dual_module.__call__ = module_call
            
            # Also add the class as an attribute
            dual_module.ate_rm = ate_rm_class
            dual_module.ATE_RM = ate_rm_class
        
        # Special handling for other handlers that might be called as functions
        elif any(handler in module_name for handler in ['ATC5000NG', 'ARINC_Client', 'D3054Scope', 'B4500CPwrMeter']):
            # Get the main class from the module
            class_name = module_name.split('.')[-1]
            if hasattr(mock_module, class_name):
                handler_class = getattr(mock_module, class_name)
                
                # Make module callable
                def module_call(*args, **kwargs):
                    return handler_class(*args, **kwargs)
                
                dual_module.__call__ = module_call
                setattr(dual_module, class_name, handler_class)
        
        return dual_module
    
    def mock_import(self, name: str, globals=None, locals=None, fromlist=(), level=0):
        """Enhanced import function with complete redirection support"""
        
        try:
            # Handle TXDLib.Handlers redirections
            if name.startswith('TXDLib.Handlers'):
                mock_name = name.replace('TXDLib.Handlers', 'MockHandlers')
                try:
                    mock_module = self.original_import(mock_name, globals, locals, fromlist, level)
                    
                    # Create dual interface if needed
                    if fromlist:
                        # Handle "from TXDLib.Handlers import X" pattern
                        result_module = ModuleType(name)
                        for item in fromlist:
                            if hasattr(mock_module, item):
                                attr = getattr(mock_module, item)
                                
                                # Special handling for ate_rm
                                if item == 'ate_rm':
                                    if hasattr(attr, 'ATE_RM'):
                                        # Create callable module
                                        callable_attr = lambda *args, **kwargs: attr.ATE_RM(*args, **kwargs)
                                        # Add class attributes
                                        callable_attr.ATE_RM = attr.ATE_RM
                                        callable_attr.ate_rm = attr.ATE_RM
                                        setattr(result_module, item, callable_attr)
                                    elif hasattr(attr, 'ate_rm'):
                                        callable_attr = attr.ate_rm
                                        setattr(result_module, item, callable_attr)
                                    else:
                                        setattr(result_module, item, attr)
                                else:
                                    setattr(result_module, item, attr)
                            else:
                                # Create minimal mock if attribute doesn't exist
                                setattr(result_module, item, self.create_minimal_mock(item))
                        
                        return result_module
                    else:
                        return self.create_dual_interface_module(mock_name, mock_module)
                        
                except ImportError:
                    # Create minimal mock module
                    return self.create_minimal_mock_module(name)
            
            # Handle direct MockHandlers imports
            elif name.startswith('MockHandlers'):
                try:
                    return self.original_import(name, globals, locals, fromlist, level)
                except ImportError:
                    return self.create_minimal_mock_module(name)
            
            # Handle common third-party modules
            elif name in ['matplotlib', 'numpy', 'pyvisa', 'niscope', 'ftd2xx', 'toml', 'reedsolo', 'visa', 'clr']:
                try:
                    mock_name = f'MockHandlers.{name}'
                    return self.original_import(mock_name, globals, locals, fromlist, level)
                except ImportError:
                    return self.create_minimal_mock_module(name)
            
            # Handle matplotlib.pyplot specifically
            elif name == 'matplotlib.pyplot':
                try:
                    mock_module = self.original_import('MockHandlers.matplotlib', globals, locals, ('pyplot',), level)
                    return mock_module.pyplot
                except ImportError:
                    return self.create_minimal_mock_module(name)
            
            # Handle System imports (for .NET)
            elif name == 'System':
                try:
                    mock_module = self.original_import('MockHandlers.mock_clr', globals, locals, ('System',), level)
                    return mock_module.System
                except ImportError:
                    return self.create_minimal_mock_module(name)
            
            # Default import
            else:
                return self.original_import(name, globals, locals, fromlist, level)
                
        except Exception as e:
            # Fallback to minimal mock for any import that fails
            return self.create_minimal_mock_module(name)
    
    def create_minimal_mock_module(self, name: str) -> ModuleType:
        """Create a minimal mock module for missing dependencies"""
        
        if name in self.mock_modules:
            return self.mock_modules[name]
        
        mock_module = ModuleType(name)
        
        # Add common attributes that might be accessed
        mock_module.__file__ = f"<mock {name}>"
        mock_module.__package__ = name.split('.')[0] if '.' in name else None
        
        # Make it callable if it's a handler
        if any(handler in name for handler in ['ate_rm', 'ATC5000NG', 'ARINC_Client', 'D3054Scope', 'B4500CPwrMeter']):
            def mock_call(*args, **kwargs):
                return MinimalMockObject()
            mock_module.__call__ = mock_call
        
        # Add getattr to handle any attribute access
        def mock_getattr(attr_name):
            if attr_name.startswith('_'):
                raise AttributeError(f"module '{name}' has no attribute '{attr_name}'")
            return MinimalMockObject()
        
        mock_module.__getattr__ = mock_getattr
        
        self.mock_modules[name] = mock_module
        return mock_module

class MinimalMockObject:
    """Minimal mock object that can handle any method call or attribute access"""
    
    def __init__(self, name="MockObject"):
        self._name = name
    
    def __call__(self, *args, **kwargs):
        return MinimalMockObject(f"{self._name}_call")
    
    def __getattr__(self, name):
        if name.startswith('_'):
            raise AttributeError(f"'{self._name}' object has no attribute '{name}'")
        return MinimalMockObject(f"{self._name}.{name}")
    
    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
        # Ignore other attribute assignments
    
    def __str__(self):
        return f"<{self._name}>"
    
    def __repr__(self):
        return f"<{self._name}>"
    
    def __bool__(self):
        return True
    
    def __len__(self):
        return 0
    
    def __iter__(self):
        return iter([])

def install_advanced_import_system():
    """Install the advanced import system"""
    redirector = TXDImportRedirector()
    return redirector

def test_import_system():
    """Test the import system with common patterns"""
    print("Testing advanced import system...")

    # Install the system
    redirector = install_advanced_import_system()

    try:
        # Test 1: Module import + function call (the problematic pattern)
        print("Test 1: from TXDLib.Handlers import ate_rm; rm = ate_rm()")

        # Create a test namespace
        test_globals = {}
        exec("from TXDLib.Handlers import ate_rm", test_globals)
        exec("rm = ate_rm()", test_globals)

        # Verify the result
        if 'rm' in test_globals and test_globals['rm'] is not None:
            print("✅ Test 1 passed")
        else:
            print("❌ Test 1 failed - rm is None")
            return False

        # Test 2: Direct class import
        print("Test 2: from TXDLib.Handlers import ATC5000NG")
        test_globals2 = {}
        exec("from TXDLib.Handlers import ATC5000NG", test_globals2)

        if 'ATC5000NG' in test_globals2:
            print("✅ Test 2 passed")
        else:
            print("❌ Test 2 failed - ATC5000NG not imported")
            return False

        # Test 3: Third-party imports
        print("Test 3: import numpy, matplotlib")
        test_globals3 = {}
        exec("import numpy", test_globals3)
        exec("import matplotlib.pyplot as plt", test_globals3)

        if 'numpy' in test_globals3 and 'plt' in test_globals3:
            print("✅ Test 3 passed")
        else:
            print("❌ Test 3 failed - numpy or plt not imported")
            return False

        print("🎉 All import tests passed!")
        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_import_system()
