"""
Mock implementation of ate_rm.py - ATE Resource Manager
Provides mock VISA resource management and logging without external dependencies
"""

import sys
import time
from typing import Dict, Any


class MockResourceManager:
    """Mock VISA Resource Manager"""
    def __init__(self):
        self.resources = {}
        
    def open_resource(self, resource_string: str):
        """Mock resource opening"""
        return MockInstrument(resource_string)
        
    def close(self):
        """Mock resource manager cleanup"""
        pass


class MockInstrument:
    """Mock VISA Instrument"""
    def __init__(self, resource_string: str):
        self.resource_string = resource_string
        self.connected = True
        
    def write(self, command: str):
        """Mock instrument write"""
        time.sleep(0.001)  # Minimal delay for realism
        
    def query(self, command: str) -> str:
        """Mock instrument query with realistic responses"""
        time.sleep(0.002)  # Minimal delay for realism
        
        # Provide realistic responses for common queries
        if "*IDN?" in command.upper():
            return "Mock Instrument,Model123,SN456789,FW1.0"
        elif "STATUS?" in command.upper():
            return "20"  # Ready status
        elif "FREQ?" in command.upper():
            return "1030.0"
        elif "POWER?" in command.upper():
            return "-20.5"
        else:
            return "OK"
            
    def close(self):
        """Mock instrument close"""
        self.connected = False


class MockObserverClient:
    """Mock Honeywell Observer Client"""
    def __init__(self):
        self.connected = True
        
    def SendMessageToObserver(self, channel: str, severity: int, facility: str, message: str):
        """Mock message sending to observer"""
        pass
        
    def Dispose(self):
        """Mock observer cleanup"""
        self.connected = False


# ATE object to manage the visa resource manager and trace log handle
class ATE_RM:
    def __init__(self):
        self.rm = MockResourceManager()
        self.tvl = MockObserverClient()
        self.logMessage(1, "Mock Resource Manager Initialized.")
        self.instruments: Dict[str, Any] = {}
            
    # Send message to tracelog
    def logMessage(self, severity: int, msg: str) -> str:
        # grab the filename and function of where this is called from (one below this in call stack)
        try:
            facility = "TXD Python Lib: " + sys._getframe(1).f_code.co_filename.split("\\")[-1] + "->" + sys._getframe(1).f_code.co_name
        except:
            facility = "TXD Python Lib: MockHandler"

        self.tvl.SendMessageToObserver("0", severity, facility, msg)
        print(f"[MOCK] {facility}: {msg}")
        return facility + " -- " + msg

    # Cleanup the visa resource manager and trace log handle
    def cleanup(self):
        self.rm.close()
        self.logMessage(1, "Mock Resource Manager Closed.")
        self.tvl.Dispose()

    def addInstrument(self, name: str, inst: Any):
        self.instruments[name] = inst

    def close(self):
        """Close resource manager"""
        self.cleanup()


# Function-style interface for backward compatibility
def ate_rm():
    """Create and return ATE_RM instance (function-style interface)"""
    return ATE_RM()
