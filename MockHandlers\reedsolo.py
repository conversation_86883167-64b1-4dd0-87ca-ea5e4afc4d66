"""
Mock reedsolo module for Reed-Solomon error correction
"""

class RSCodec:
    """Mock Reed-Solomon codec"""
    
    def __init__(self, nsym=10):
        self.nsym = nsym
    
    def encode(self, data):
        """Mock encoding - just return data with mock parity"""
        if isinstance(data, str):
            data = data.encode()
        elif isinstance(data, list):
            data = bytes(data)
        
        # Add mock parity bytes
        parity = b'\x00' * self.nsym
        return data + parity
    
    def decode(self, data):
        """Mock decoding - just return original data"""
        if isinstance(data, str):
            data = data.encode()
        elif isinstance(data, list):
            data = bytes(data)
        
        # Remove parity bytes
        if len(data) > self.nsym:
            return data[:-self.nsym]
        return data

def rs_encode_msg(msg_in, nsym):
    """Mock Reed-Solomon encoding function"""
    codec = RSCodec(nsym)
    return codec.encode(msg_in)

def rs_decode_msg(msg_in, nsym):
    """Mock Reed-Solomon decoding function"""
    codec = RSCodec(nsym)
    return codec.decode(msg_in)

def rs_correct_msg(msg_in, nsym):
    """Mock Reed-Solomon error correction"""
    codec = RSCodec(nsym)
    corrected = codec.decode(msg_in)
    return corrected, []  # Return corrected message and empty error list
