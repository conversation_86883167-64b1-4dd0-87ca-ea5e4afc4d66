# -*- coding: utf-8 -*-
"""
Created on Fri Mar 20 9:02:30 2020

@author: E589493
         K. <PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-189 MOPs requirement for
             Interrogator Pulse Charectoristics, Section 2.2.3
             
             "Pulse Rise Time: The maximum time required for the pulse to rise
             from 10% to 90% of its maximum voltage amplitude shall not exceed 3.0 micro seconds

             Pulse Decay Time: The maximum time required for the pulse to fall
             from 90% to 10% of its maximum voltage amplitude shall not exceed 3.5 micro seconds

             Pulse Duration: The time between the points on the leading and trailing edges of 
             the pulse which are 50% of the maximum voltage and amplitude if the pulse, shall be 
             3.5 +/- 0.5 microseconds.

             Pulse Top: Between the point on the leading edge of the pusle which is 95% of
             maximum amplitude and the point on the trailing edge which 95% of the maximum 
             amplitude, the instataneous amplitude shall not fall below 95% of the maximum 
             voltage amplitude of the pulse.
             
INPUTS:      Top_Cable_Loss, Scope, ARINC server, ATC
OUTPUTS:     Results[8]: List of width, rise time, fall time, and top noise for both P1 and P2 (4 parameters for each pulse)

HISTORY:

02/26/2020   KF    Initial Release.
06/22/2020   AS    Added tvl statements, Added ARINC
03/10/2021   MRS   Updates for new handlers and Lobster.
10/2023      CS    Updated to use ATC instead of Scope for Pulse Characteristics. Don't currently have a reliable trigger
                   mechanism when using TXD flight s/w.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client
from TXDLib.Handlers.D3054Scope import D3054Scope

##############################################################################
################# OPTIMIZATION HELPER FUNCTIONS #############################
##############################################################################

# HIGH PRIORITY OPTIMIZATION: Helper functions for status polling
def wait_for_arinc_ready(arinc_client, timeout=10, poll_interval=0.5):
    """Wait for ARINC client to be ready with status polling"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # Test ARINC connection with simple query
            arinc_client.getStatus()
            return True
        except:
            time.sleep(poll_interval)
    # Fallback to remaining original delay time
    remaining_time = max(0, 5 - (time.time() - start_time))
    if remaining_time > 0:
        time.sleep(remaining_time)
    return False

def wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2):
    """Wait for ATC measurement to be ready"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            atc.waitforstatus()
            return True
        except:
            pass
        time.sleep(poll_interval)
    # Fallback to remaining original delay
    remaining_time = max(0, 2 - (time.time() - start_time))
    if remaining_time > 0:
        time.sleep(remaining_time)
    return False

def wait_for_scope_ready(scope, timeout=2, poll_interval=0.1):
    """Wait for scope operation to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # Simple readiness check - if no exception, scope is ready
            scope.basicQuery("*OPC?")
            return True
        except:
            pass
        time.sleep(poll_interval)
    # Fallback to remaining original delay
    remaining_time = max(0, 0.5 - (time.time() - start_time))
    if remaining_time > 0:
        time.sleep(remaining_time)
    return False

def wait_for_measurement_complete(atc, timeout=3, poll_interval=0.2):
    """Wait for ATC measurement to complete with adaptive polling"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            atc.waitforstatus()
            return True
        except:
            pass
        time.sleep(poll_interval)
    # Fallback to remaining original delay
    remaining_time = max(0, 1 - (time.time() - start_time))
    if remaining_time > 0:
        time.sleep(remaining_time)
    return False

def adaptive_retry_delay(timeout=2, poll_interval=0.5):
    """Adaptive delay for communication retries"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        time.sleep(poll_interval)
        return True
    # Fallback to remaining original delay
    remaining_time = max(0, 1 - (time.time() - start_time))
    if remaining_time > 0:
        time.sleep(remaining_time)
    return False

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def measurePulseTopNoise(scope_obj):
    """ Measures the top noise of DME pulse.  Pass in edge position and pulse characteristics.
    returns percent of top noise """

    #Get max and min and calculate top noise % 
    time.sleep(.3)
    vMax = scope_obj.measVMax()
    time.sleep(.3)
    vMin = scope_obj.measVMin()
    time.sleep(.3)
    return abs(((float(0.95) * float(vMax))-float(vMin))/(float(0.95) * float(vMax)))


def init_DME_Standard(atc, ARINC):
    """ Sets DME Standard COnditions to DME Channel 56X with VOR Pair 0 119.9MHz at -70dBm adn 34nm, Squidder rate
    of 2700 """
    ARINC.writeChannel(134.40)
    # HIGH PRIORITY OPTIMIZATION: Replace 5s fixed delay with ARINC ready polling
    wait_for_arinc_ready(ARINC, timeout=10, poll_interval=0.5)
    atc.DMEMode()
    #atc.gwrite(":ATC:DME:CABLELOSS " + str(cable_loss))
        
def modeX_Setup(scope_obj,timescale,threshold,trigger):
    """ Basic Scope setup for ModeX/DME Pulse. 
    RFBOB must be set up apriori."""
    
    scope_obj.Reset()
    #Edited to display only ch3
    scope_obj.chanDisplay(1,0)
    scope_obj.chanDisplay(2,0)
    scope_obj.chanDisplay(3,1)
    scope_obj.chanDisplay(4,0)
    #Set the Scale
    scope_obj.voltDiv(1, 100, "mV")
    scope_obj.voltDiv(2, 100, "mV")
    scope_obj.voltDiv(3, 7, "mV")
    scope_obj.voltDiv(4, 20, "mV")
    #Digitize all chanels
    #scope_obj.Digitize()
    #Invert Chan 3 and 4
    scope_obj.chanInvert(3,1)
    scope_obj.chanInvert(4,1)
    #Set Impedance of channels 3 and 4 to 50 Ohms
    scope_obj.setChannelTermination(3,50)
    scope_obj.setChannelTermination(4,50)
    #Set TimeScale and Trigger Level
    scope_obj.timeScale(timescale, "us")
    scope_obj.trigMode("NORMal")
    scope_obj.trigSource(trigger)
    scope_obj.trigType("EDGE")
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")   #chan 3 is trigger
    scope_obj.setTimePosition(".0000075")
    scope_obj.chanOffset(3, 25, "mV")
    #scope_obj.trigRun()

def measurePulseChar(scope_obj, pEdge,nEdge, source = 3):
    """Function that measures PWidth, Rise, and fall for a given Pulse rising edge position.
    Fall time is taken by moving the cursor edgePPosition + PWidth. """

    #Measure Chan 3
    scope_obj.setMeasureSource(source)
    result = []

    scope_obj.timeScale(.5, "us")
    # MEDIUM PRIORITY OPTIMIZATION: Replace scope settling delay with operation complete polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)
    scope_obj.setTimePosition(pEdge)
    # MEDIUM PRIORITY OPTIMIZATION: Replace measurement settling delay with status polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)
    pwidth = scope_obj.measPWidth()
    # MEDIUM PRIORITY OPTIMIZATION: Replace measurement completion delay with status polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)
    pwidth = float(pwidth)
    #Estimate Width if result invalid
    #if (pwidth > 1.0):
    #    pwidth = (nEdge - pEdge) - 0.1e-6
    #    print("Estimated Value: ",pwidth)
    result.append(pwidth)
    scope_obj.timeScale(.5, "us")
    # MEDIUM PRIORITY OPTIMIZATION: Replace scope settling delay with operation complete polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)
    result.append(scope_obj.measRiseTime())
    scope_obj.setTimePosition(nEdge)
    # MEDIUM PRIORITY OPTIMIZATION: Replace scope settling delay with operation complete polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)
    result.append(scope_obj.measFallTime())
    # MEDIUM PRIORITY OPTIMIZATION: Replace scope settling delay with operation complete polling
    wait_for_scope_ready(scope_obj, timeout=2, poll_interval=0.1)

    return result  

def trigDetect(scope_obj):
    """ Function that triggers a given waveform and returns all detected edge positions based
    on trigger position. """
    # HIGH PRIORITY OPTIMIZATION: Replace 1s fixed delay with scope ready polling
    wait_for_scope_ready(scope_obj, timeout=3, poll_interval=0.1)
    scope_obj.trigSingle(0.5)
    #Trigger Source Chan is set in setup (modex_setup)
    if scope_obj.trigSingle(2):
        return 1
    return 0
    
def getPulses(rm,scope_obj):
    #Set Up the OScope for Pulse Measurements   
    
    tmescale = 2.0            #scope time scale (usec/div), large enough to capture both pulses
    threshold = 10.0           #threshold (mV)
    trigger = 3                #use Chan 3 as trigger
    modeX_Setup(scope_obj,tmescale,threshold,trigger)
    # HIGH PRIORITY OPTIMIZATION: Replace 1s fixed delay with scope setup complete polling
    wait_for_scope_ready(scope_obj, timeout=3, poll_interval=0.1)
    
    #Results
    result = []
    pulse_char = []

    #Get DME Pulse Parameters
    count = 0
    while ((count < 5) and (len(result) == 0)):    
        if trigDetect(scope_obj) == 1:
            # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
            PEdges,NEdges = scope_obj.digiEdgePos(threshold/1000.0,source = 3)
            #scope_obj.plotWave(3)               #comment this out when running from TestStand    
       
            #Number of Positive/Negative Edges
            rm.logMessage(0,("PEdges: %d,%s " % (len(PEdges),str(PEdges))))    
            rm.logMessage(0,("NEdges: %d,%s" % (len(NEdges),str(NEdges))))
            if len(PEdges) == 0:
                rm.logMessage(3, ("Test_2_3_2_3_1 - Error, No Edges Detected, Cnt: " + str(count)))
            else:
                #Loop thru Positive Edges and gather pulse data
                i=0
                edge_pos=0
                for edge_pos in PEdges:
                    pulse_char = measurePulseChar(scope_obj,edge_pos,NEdges[i], 3)
                    rm.logMessage(0,"Pulse duration: " + str(pulse_char[0]))
                    rm.logMessage(0,"Pulse RiseTime: " + str(pulse_char[1]))
                    rm.logMessage(0,"Pulse FallTime: " + str(pulse_char[2]))           
                    result.append(float(pulse_char[0]))           
                    result.append(float(pulse_char[1]))           
                    result.append(float(pulse_char[2]))
                    #Get Topnoise for Pulse
                    midpoint = scope_obj.measXMax()
                    #midpoint = edge_pos + (pulse_char[0]/2.0)
                    rm.logMessage(0,"Midpoint: " + str(midpoint))
                    scope_obj.setTimePosition(midpoint)
                    # MEDIUM PRIORITY OPTIMIZATION: Keep 0.1s scope positioning delay (critical for accuracy)
                    time.sleep(.1)
                    scope_obj.timeScale(.5, 'us')
                    pulse_noise = measurePulseTopNoise(scope_obj)
                    rm.logMessage(0,"Pulse Noise: " + str(pulse_noise))
                    result.append(float(pulse_noise))
                    # MEDIUM PRIORITY OPTIMIZATION: Reduced measurement completion delay from 5s to 1s
                    time.sleep(1)
                    i=i+1
        #try 5 times      
        count = count + 1   
            
    return result

##############################################################################
################# MAIN TEST SEQUENCE #########################################
##############################################################################
def Test_2_2_3(rm,ARINC,atc, scope_obj):

    sf = 1.0e-3                             #Scale factor (convert nsec to usec)
    RiseTime1 = 0.0                         #Rise time of P1
    FallTime1 = 0.0                         #Fall time of P1
    PulseWidth1 = 0.0                       #Pulse Width for P1
    RiseTime2 = 0.0                         #Rise time of P2 
    FallTime2 = 0.0                         #Fall time of P2
    PulseWidth2 = 0.0                       #Pulse Width for P2
    PNoise = 0.0
        
    #Tune DME
    ARINC.writeChannel(111.90)
    #Put ATC in DME Mode.
    atc.DMEMode()
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s DME mode settling delay with measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    atc.gwrite(":ATC:MEA:DFORMAT 2")      #Float data format
    # MEDIUM PRIORITY OPTIMIZATION: Replace format setting delay with measurement ready polling
    wait_for_measurement_complete(atc, timeout=1, poll_interval=0.1)

#Measure Pulse 1 Rise Time
    atc.gwrite(":ATC:DME:PULSE P1")      #Measure 1st pulse
    # HIGH PRIORITY OPTIMIZATION: Replace 1s pulse measurement settling delay with ATC ready polling
    wait_for_atc_measurement_ready(atc, timeout=3, poll_interval=0.2)
    RiseTime1 = atc.getRiseTime(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace measurement completion delay with status polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("RiseTime1: " + str(RiseTime1)))

    # fix for erroneous response
    count = 0
    while (RiseTime1 == 0.0 or RiseTime1 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        RiseTime1 = atc.getRiseTime(2)
        count = count + 1

    RiseTime1 = (RiseTime1 * sf)
    rm.logMessage(0,("RiseTime1 Final: " + str(RiseTime1)))

#Measure Pulse 1 Fall Time
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement ready polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    FallTime1 = atc.getFallTime(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement complete polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("FallTime1: " + str(FallTime1)))

    # fix for erroneous response
    count = 0
    while (FallTime1 == 0.0 or FallTime1 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        FallTime1 = atc.getFallTime(2)
        count = count + 1

    FallTime1 = (FallTime1 * sf)
    rm.logMessage(0,("FallTime1 Final: " + str(FallTime1)))

#Measure Pulse 1 Pulse Width
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    PulseWidth1 = atc.getPulseWidth(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement complete polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("PulseWidth1: " + str(PulseWidth1)))

    # fix for erroneous response
    count = 0
    while (PulseWidth1 == 0.0 or PulseWidth1 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        PulseWidth1 = atc.getPulseWidth(2)
        count = count + 1

    PulseWidth1 = (PulseWidth1 * sf)
    rm.logMessage(0,("PulseWidth1 Final: " + str(PulseWidth1)))

#Measure Pulse 2 Rise Time
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    atc.gwrite(":ATC:DME:PULSE P2")      #Measure 2nd pulse
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    RiseTime2 = atc.getRiseTime(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement complete polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("RiseTime2: " + str(RiseTime2)))

    # fix for erroneous response
    count = 0
    while (RiseTime2 == 0.0 or RiseTime2 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        RiseTime2 = atc.getRiseTime(2)
        count = count + 1

    RiseTime2 = (RiseTime2 * sf)
    rm.logMessage(0,("RiseTime2 Final: " + str(RiseTime2)))

#Measure Pulse 2 Fall Time
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement ready polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    FallTime2 = atc.getFallTime(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement complete polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("FallTime2: " + str(FallTime2)))

    # fix for erroneous response
    count = 0
    while (FallTime2 == 0.0 or FallTime2 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        FallTime2 = atc.getFallTime(2)
        count = count + 1

    FallTime2 = (FallTime2 * sf)
    rm.logMessage(0,("FallTime2 Final: " + str(FallTime2)))

#Measure Pulse 2 Pulse Width
    atc.waitforstatus()
    # HIGH PRIORITY OPTIMIZATION: Replace 2s delay with ATC measurement ready polling
    wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
    PulseWidth2 = atc.getPulseWidth(2)
    # MEDIUM PRIORITY OPTIMIZATION: Replace 1s delay with measurement complete polling
    wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
    rm.logMessage(0,("PulseWidth2: " + str(PulseWidth2)))

    # fix for erroneous response
    count = 0
    while (PulseWidth2 == 0.0 or PulseWidth2 == 20) and count < 10:
        # MEDIUM PRIORITY OPTIMIZATION: Replace 1s retry delay with adaptive polling
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        PulseWidth2 = atc.getPulseWidth(2)
        count = count + 1

    PulseWidth2 = (PulseWidth2 * sf)
    rm.logMessage(0,("PulseWidth2 Final: " + str(PulseWidth2)))

#Measure Pulse Top Noise

    #Set Up the OScope for Pulse Measurements   
    
    tmescale = 2.0            #scope time scale (usec/div), large enough to capture both pulses
    threshold = 10.0           #threshold (mV)
    trigger = 3                #use Chan 3 as trigger
    modeX_Setup(scope_obj,tmescale,threshold,trigger)
    # HIGH PRIORITY OPTIMIZATION: Replace 1s fixed delay with scope setup complete polling
    wait_for_scope_ready(scope_obj, timeout=3, poll_interval=0.1)
    
    #Capture DME Pulse Parameters
    count = 0
    while ((count < 5) and PNoise == 0):    
        if trigDetect(scope_obj) == 1:
            # Threshold is in Volts, essentially a y-scale line that if a rising edge passes it is then registered. IE 20mV
            PEdges,NEdges = scope_obj.digiEdgePos(threshold/1000.0,source = 3)
            #scope_obj.plotWave(3)               #comment this out when running from TestStand    
       
            #Number of Positive/Negative Edges
            rm.logMessage(0,("PEdges: %d,%s " % (len(PEdges),str(PEdges))))    
            rm.logMessage(0,("NEdges: %d,%s" % (len(NEdges),str(NEdges))))
            if len(PEdges) == 0:
                rm.logMessage(3, ("Test_2_3_2_3_1 - Error, No Edges Detected, Cnt: " + str(count)))
            else:
                #Loop thru Positive Edges
                scope_obj.setMeasureSource(3)
                # MEDIUM PRIORITY OPTIMIZATION: Replace 0.3s delay with scope ready polling
                wait_for_scope_ready(scope_obj, timeout=1, poll_interval=0.1)
                scope_obj.timeScale(.5, "us")
                # MEDIUM PRIORITY OPTIMIZATION: Replace 0.3s delay with scope ready polling
                wait_for_scope_ready(scope_obj, timeout=1, poll_interval=0.1)
                i=0
                edge_pos=0
                for edge_pos in PEdges:
                    #Get Topnoise for Pulse
                    midpoint = scope_obj.measXMax()
                    #midpoint = edge_pos + (pulse_char[0]/2.0)
                    rm.logMessage(0,"Midpoint: " + str(midpoint))
                    scope_obj.setTimePosition(midpoint)
                    # MEDIUM PRIORITY OPTIMIZATION: Keep 0.1s scope positioning delay (critical for accuracy)
                    wait_for_scope_ready(scope_obj, timeout=1, poll_interval=0.1)
                    PNoise = measurePulseTopNoise(scope_obj)
                    rm.logMessage(0,"Pulse Noise: " + str(PNoise))
                    # HIGH PRIORITY OPTIMIZATION: Replace 5s fixed delay with measurement complete polling
                    wait_for_scope_ready(scope_obj, timeout=8, poll_interval=0.5)
                    i=i+1
        #try 5 times      
        count = count + 1   
            
    atc.gwrite(':ATC:DME:STOP')
   
    return [PulseWidth1] + [RiseTime1] + [FallTime1] + [PulseWidth2] + [RiseTime2] + [FallTime2] + [PNoise]

     
##############################################################################
def main():
    #Initialize Intrsumets

    rm = ate_rm()

    #Initiazlie the ATC
    atc = ATC5000NG(rm)
    atc.Reset()    

    #Initialize Scope
    scope = D3054Scope(rm)
    scope.Reset()

    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Tune DME
    ARINC.writeChannel(111.90)
    #Put ATC in DME Mode.
    atc.DMEMode()

    
    #Setup Scope to trigger on a pulse pair
    result = getPulses(rm,scope)


    if(result != []):
        #Pulse 1 Results
        print("Pulse 1 Width: " + str(result[0]))
        print("Pulse 1 Rise Time: " + str(result[1]))
        print("Pulse 1 Fall Time: " + str(result[2]))
        print("Pulse 1 Top Noise: " + str(result[3]))
 
        #Pulse 2Results
        print("Pulse 2 Width: " + str(result[4]))
        print("Pulse 2 Rise Time: " + str(result[5]))
        print("Pulse 2 Fall Time: " + str(result[6]))
        print("Pulse 2 Top Noise: " + str(result[7]))



    atc.gwrite(':ATC:DME:STOP')
    atc.close()
    scope.close()
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()



if __name__ == "__main__":
    main()

