{"report_info": {"report_type": "TXD System Test - Consolidated Report", "generation_timestamp": "2025-06-07T20:13:41.565432", "execution_mode": "mock", "report_version": "1.0.0"}, "execution_summary": {"execution_mode": "mock", "total_sequences": 44, "passed": 0, "failed": 44, "errors": 0, "timeouts": 0, "success_rate": 0.0, "total_execution_time": 41.67939901351929, "start_time": "2025-06-07T20:12:59.885362", "end_time": "2025-06-07T20:13:41.564761"}, "sequence_results": [{"procedure": "DO189", "sequence": "DO_189_2_2_10.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0019354820251465, "start_time": "2025-06-07T20:12:59.886700", "end_time": "2025-06-07T20:13:00.888636", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_10.py\", line 458, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0271575450897217, "start_time": "2025-06-07T20:13:00.889930", "end_time": "2025-06-07T20:13:01.917089", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_12.py\", line 56, in <module>\n    from TXDLib.Handlers import audio_processing as ap\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\audio_processing.py\", line 43, in <module>\n    import niscope                     #routines for the NI PXI Scope\n    ^^^^^^^^^^^^^^\nModuleNotFoundError: No module named 'niscope'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_1_b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9125716686248779, "start_time": "2025-06-07T20:13:01.918373", "end_time": "2025-06-07T20:13:02.830946", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 127, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_1_b.py\", line 91, in main\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9832684993743896, "start_time": "2025-06-07T20:13:02.831741", "end_time": "2025-06-07T20:13:03.815010", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_3.py\", line 48, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\D3054Scope.py\", line 28, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9559106826782227, "start_time": "2025-06-07T20:13:03.815916", "end_time": "2025-06-07T20:13:04.771828", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_4.py\", line 39, in <module>\n    from TXDLib.Handlers.D3054Scope import D3054Scope\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\D3054Scope.py\", line 28, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_6.py", "status": "FAILED", "return_code": 1, "execution_time": 1.001603126525879, "start_time": "2025-06-07T20:13:04.772770", "end_time": "2025-06-07T20:13:05.774376", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_6.py\", line 36, in <module>\n    from TXDLib.Handlers import N9010BSpecAn\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\N9010BSpecAn.py\", line 24, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9609026908874512, "start_time": "2025-06-07T20:13:05.775554", "end_time": "2025-06-07T20:13:06.736456", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_7.py\", line 54, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO189", "sequence": "DO_189_2_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9468142986297607, "start_time": "2025-06-07T20:13:06.737585", "end_time": "2025-06-07T20:13:07.684400", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_2_2_8.py\", line 247, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO189", "sequence": "DO_189_DME_SG_Load.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9371054172515869, "start_time": "2025-06-07T20:13:07.685152", "end_time": "2025-06-07T20:13:08.622258", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO189\\DO_189_DME_SG_Load.py\", line 84, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.995518684387207, "start_time": "2025-06-07T20:13:08.623497", "end_time": "2025-06-07T20:13:09.619015", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a.py\", line 49, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9822041988372803, "start_time": "2025-06-07T20:13:09.619832", "end_time": "2025-06-07T20:13:10.602037", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py\", line 49, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9694404602050781, "start_time": "2025-06-07T20:13:10.602803", "end_time": "2025-06-07T20:13:11.572244", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1b.py\", line 48, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step1c.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9482932090759277, "start_time": "2025-06-07T20:13:11.573143", "end_time": "2025-06-07T20:13:12.521437", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step1c.py\", line 48, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a.py", "status": "FAILED", "return_code": 1, "execution_time": 1.0011420249938965, "start_time": "2025-06-07T20:13:12.522307", "end_time": "2025-06-07T20:13:13.523451", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a.py\", line 58, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2a_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9801435470581055, "start_time": "2025-06-07T20:13:13.524721", "end_time": "2025-06-07T20:13:14.504865", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py\", line 58, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9548232555389404, "start_time": "2025-06-07T20:13:14.505690", "end_time": "2025-06-07T20:13:15.460514", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step2b.py\", line 58, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_10_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9650135040283203, "start_time": "2025-06-07T20:13:15.461851", "end_time": "2025-06-07T20:13:16.426865", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_10_Step3.py\", line 48, in <module>\n    from TXDLib.Handlers import RFBOB\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\RFBOB.py\", line 14, in <module>\n    import ftd2xx as ftd\nModuleNotFoundError: No module named 'ftd2xx'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_12.py", "status": "FAILED", "return_code": 1, "execution_time": 0.4142754077911377, "start_time": "2025-06-07T20:13:16.427791", "end_time": "2025-06-07T20:13:16.842067", "stdout": "", "stderr": "C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py:53: SyntaxWarning: invalid escape sequence '\\P'\n  clr.AddReference(\"C:\\Program Files\\Honeywell\\Lobster4\\ExternalLibraries\\Observer\\Interface\")\nTraceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_12.py\", line 38, in <module>\n    import atc\nModuleNotFoundError: No module named 'atc'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9144942760467529, "start_time": "2025-06-07T20:13:16.842808", "end_time": "2025-06-07T20:13:17.757303", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step1.py\", line 132, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.935452938079834, "start_time": "2025-06-07T20:13:17.758467", "end_time": "2025-06-07T20:13:18.693922", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step2.py\", line 128, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9158675670623779, "start_time": "2025-06-07T20:13:18.694778", "end_time": "2025-06-07T20:13:19.610647", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step3.py\", line 153, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9602475166320801, "start_time": "2025-06-07T20:13:19.611380", "end_time": "2025-06-07T20:13:20.571628", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step4.py\", line 101, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9223365783691406, "start_time": "2025-06-07T20:13:20.572398", "end_time": "2025-06-07T20:13:21.494737", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step5.py\", line 113, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9371087551116943, "start_time": "2025-06-07T20:13:21.495472", "end_time": "2025-06-07T20:13:22.432580", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step6.py\", line 109, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_1_step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9238142967224121, "start_time": "2025-06-07T20:13:22.433775", "end_time": "2025-06-07T20:13:23.357592", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_1_step7.py\", line 95, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9886362552642822, "start_time": "2025-06-07T20:13:23.358573", "end_time": "2025-06-07T20:13:24.347207", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_1.py\", line 128, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9835894107818604, "start_time": "2025-06-07T20:13:24.348304", "end_time": "2025-06-07T20:13:25.331894", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2.py\", line 47, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_2_2_11-14-23.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9621500968933105, "start_time": "2025-06-07T20:13:25.333264", "end_time": "2025-06-07T20:13:26.295416", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_2_2_11-14-23.py\", line 47, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9797794818878174, "start_time": "2025-06-07T20:13:26.297054", "end_time": "2025-06-07T20:13:27.276836", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1.py\", line 52, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_1_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9428601264953613, "start_time": "2025-06-07T20:13:27.278090", "end_time": "2025-06-07T20:13:28.220951", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_1_old.py\", line 50, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9590263366699219, "start_time": "2025-06-07T20:13:28.222313", "end_time": "2025-06-07T20:13:29.181341", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a.py\", line 57, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2a_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9862065315246582, "start_time": "2025-06-07T20:13:29.182674", "end_time": "2025-06-07T20:13:30.168881", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2a_old.py\", line 55, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9683914184570312, "start_time": "2025-06-07T20:13:30.169652", "end_time": "2025-06-07T20:13:31.138044", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b.py\", line 40, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_3_2b_old.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9602608680725098, "start_time": "2025-06-07T20:13:31.139324", "end_time": "2025-06-07T20:13:32.099585", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_3_2b_old.py\", line 40, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9768829345703125, "start_time": "2025-06-07T20:13:32.100488", "end_time": "2025-06-07T20:13:33.077372", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_4.py\", line 50, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib'\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step1.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9337129592895508, "start_time": "2025-06-07T20:13:33.078218", "end_time": "2025-06-07T20:13:34.011933", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step1.py\", line 114, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step2.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9511122703552246, "start_time": "2025-06-07T20:13:34.012977", "end_time": "2025-06-07T20:13:34.964091", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step2.py\", line 114, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step3.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9304678440093994, "start_time": "2025-06-07T20:13:34.965187", "end_time": "2025-06-07T20:13:35.895657", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step3.py\", line 191, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step4.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9642467498779297, "start_time": "2025-06-07T20:13:35.897185", "end_time": "2025-06-07T20:13:36.861433", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step4.py\", line 122, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step5.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9164795875549316, "start_time": "2025-06-07T20:13:36.862815", "end_time": "2025-06-07T20:13:37.779296", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step5.py\", line 192, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step6.py", "status": "FAILED", "return_code": 1, "execution_time": 0.9642510414123535, "start_time": "2025-06-07T20:13:37.780348", "end_time": "2025-06-07T20:13:38.744600", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step6.py\", line 209, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step7.py", "status": "FAILED", "return_code": 1, "execution_time": 0.933021068572998, "start_time": "2025-06-07T20:13:38.745365", "end_time": "2025-06-07T20:13:39.678387", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step7.py\", line 182, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_5_Step8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.954906702041626, "start_time": "2025-06-07T20:13:39.679321", "end_time": "2025-06-07T20:13:40.634228", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_5_Step8.py\", line 135, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}, {"procedure": "DO181", "sequence": "DO_181E_2_3_2_8.py", "status": "FAILED", "return_code": 1, "execution_time": 0.928905725479126, "start_time": "2025-06-07T20:13:40.635495", "end_time": "2025-06-07T20:13:41.564402", "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO181\\DO_181E_2_3_2_8.py\", line 117, in <module>\n    rm = ate_rm()\nTypeError: 'module' object is not callable\n"}], "analysis": {"overall_status": "FAILED", "procedure_breakdown": {"DO189": {"total": 9, "passed": 0, "failed": 9, "errors": 0, "timeouts": 0}, "DO181": {"total": 35, "passed": 0, "failed": 35, "errors": 0, "timeouts": 0}}, "performance_metrics": {"total_execution_time": 41.67939901351929, "average_sequence_time": 0.9472590684890747, "sequences_per_hour": 3800.4386759180666, "optimization_effectiveness": {"optimization_success_rate": 0.0, "estimated_time_savings": "157-187 seconds per full test suite", "optimization_status": "NEEDS_REVIEW"}}, "failure_analysis": {"total_failures": 44, "failure_by_procedure": {"DO189": 9, "DO181": 35}, "common_failure_patterns": ["Hardware communication errors detected"]}, "recommendations": ["Review failed sequences and address root causes before production use", "Investigate 44 failed sequences", "Verify hardware connections before live mode execution", "Run mock mode tests regularly to validate sequence logic", "Monitor execution times for performance regression", "Keep system test reports for compliance documentation"]}}