"""
Mock numpy module for TXD testing
"""

import random
import math

class MockArray:
    """Mock numpy array"""
    
    def __init__(self, data):
        if isinstance(data, (list, tuple)):
            self.data = list(data)
        elif isinstance(data, (int, float)):
            self.data = [data]
        else:
            self.data = [0]
        self.shape = (len(self.data),)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, index):
        return self.data[index]
    
    def __setitem__(self, index, value):
        self.data[index] = value
    
    def __iter__(self):
        return iter(self.data)
    
    def mean(self):
        return sum(self.data) / len(self.data) if self.data else 0
    
    def std(self):
        if not self.data:
            return 0
        mean_val = self.mean()
        variance = sum((x - mean_val) ** 2 for x in self.data) / len(self.data)
        return math.sqrt(variance)
    
    def max(self):
        return max(self.data) if self.data else 0
    
    def min(self):
        return min(self.data) if self.data else 0
    
    def tolist(self):
        return self.data.copy()

def array(data):
    """Create mock array"""
    return MockArray(data)

def zeros(shape):
    """Create array of zeros"""
    if isinstance(shape, int):
        return MockArray([0] * shape)
    elif isinstance(shape, tuple) and len(shape) == 1:
        return MockArray([0] * shape[0])
    else:
        return MockArray([0] * 10)  # Default size

def ones(shape):
    """Create array of ones"""
    if isinstance(shape, int):
        return MockArray([1] * shape)
    elif isinstance(shape, tuple) and len(shape) == 1:
        return MockArray([1] * shape[0])
    else:
        return MockArray([1] * 10)  # Default size

def random(size=None):
    """Generate random array"""
    if size is None:
        return random.random()
    elif isinstance(size, int):
        return MockArray([random.random() for _ in range(size)])
    else:
        return MockArray([random.random() for _ in range(10)])

def arange(start, stop=None, step=1):
    """Create range array"""
    if stop is None:
        stop = start
        start = 0
    
    result = []
    current = start
    while current < stop:
        result.append(current)
        current += step
    
    return MockArray(result)

def linspace(start, stop, num=50):
    """Create linearly spaced array"""
    if num <= 1:
        return MockArray([start])
    
    step = (stop - start) / (num - 1)
    result = [start + i * step for i in range(num)]
    return MockArray(result)

# Constants
pi = math.pi
e = math.e

# Math functions
def sin(x):
    if hasattr(x, '__iter__'):
        return MockArray([math.sin(val) for val in x])
    return math.sin(x)

def cos(x):
    if hasattr(x, '__iter__'):
        return MockArray([math.cos(val) for val in x])
    return math.cos(x)

def sqrt(x):
    if hasattr(x, '__iter__'):
        return MockArray([math.sqrt(val) for val in x])
    return math.sqrt(x)

def abs(x):
    if hasattr(x, '__iter__'):
        return MockArray([abs(val) for val in x])
    return abs(x)
