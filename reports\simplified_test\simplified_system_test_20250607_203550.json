{"test_execution": {"timestamp": "2025-06-07T20:35:50.207277", "mode": "MOCK", "framework": "Simplified System Test Framework", "total_duration": 2.19, "total_execution_time": 2.18, "procedures_tested": 13, "test_scope": "Working procedures with current mock setup"}, "summary": {"total_tests": 13, "passed": 12, "failed": 1, "timeout": 0, "error": 0, "pass_rate": 92.3, "success_criteria_met": false}, "test_results": [{"procedure_path": "ate_power.py", "test_purpose": "Test procedure: ate_power", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.12, "output": "", "error": null}, {"procedure_path": "BIT.py", "test_purpose": "Collect data from RF FPGA BITE Retrieve latest list of raw signed hex xadc counts from FPGA BIT", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.14, "output": "", "error": null}, {"procedure_path": "Calibration.py", "test_purpose": "Sample API for encoding, decoding JSON files Write RF CAL request into expected data format", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "CSVFile.py", "test_purpose": "Test procedure: CSVFile", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.13, "output": "", "error": null}, {"procedure_path": "dme_burst.py", "test_purpose": "json encoder python DME_SEQ_TYPE Values X/Y receive channel", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.14, "output": "", "error": null}, {"procedure_path": "DO282\\FEC.py", "test_purpose": "-*- coding: utf-8 -*-  ", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.12, "output": "", "error": null}, {"procedure_path": "DO385\\DO385_2_2_3_3.py", "test_purpose": "-*- coding: utf-8 -*-  ", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "FAILED", "execution_time": 0.57, "output": "", "error": "Traceback (most recent call last):\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Procedures\\DO385\\DO385_2_2_3_3.py\", line 30, in <module>\n    from TXDLib.Handlers import B4500CPwrMeter\n  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\TXDLib\\Handlers\\B4500CPwrMeter.py\", line 32, in <module>\n    import matplotlib.pyplot as plt\nModuleNotFoundError: No module named 'matplotlib.pyplot'; 'matplotlib' is not a package\n"}, {"procedure_path": "DO385\\DO385_2_2_4_5_4_1.py", "test_purpose": "-*- coding: utf-8 -*-  ", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.11, "output": "", "error": null}, {"procedure_path": "PulseTiming.py", "test_purpose": "Setup Scope for TCAS pulse measurements", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.12, "output": "", "error": null}, {"procedure_path": "sensitivity.py", "test_purpose": "  main", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "Spectrum.py", "test_purpose": "Return power at specified center frequency on spectrum analyzer Setup Spectrum Analyzer aterm.instruments[\"SpecAn\"].setExternalTrigger()", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.13, "output": "", "error": null}, {"procedure_path": "SPIDevices.py", "test_purpose": "Collection of functions to initialize and control DAC104S085", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.17, "output": "", "error": null}, {"procedure_path": "txd_power.py", "test_purpose": "Test procedure: txd_power", "test_methodology": "Standard test execution procedure with mock interfaces", "status": "PASSED", "execution_time": 0.11, "output": "", "error": null}], "mock_validation": {"approach": "Direct execution with mock environment", "compatibility": "Tested procedures work with current mock interfaces", "limitations": "Full import redirection not implemented for all procedures"}}