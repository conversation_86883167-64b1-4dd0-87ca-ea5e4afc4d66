{"test_execution": {"timestamp": "2025-06-07T20:31:25.043846", "mode": "MOCK", "framework": "Comprehensive System Test Framework", "total_duration": 14.53, "total_execution_time": 14.49, "procedures_discovered": 84}, "summary": {"total_tests": 84, "passed": 11, "failed": 73, "timeout": 0, "error": 0, "pass_rate": 13.1, "success_criteria_met": false}, "test_results": [{"procedure_path": "ate_power.py", "test_purpose": "General test procedure: ate_power", "test_methodology": "Standard test execution procedure", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "BIT.py", "test_purpose": "Collect data from RF FPGA BITE  '''\n\nimport json, time, os, glob, toml, socket, base64\nfrom datetime...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: No module named 'toml'\n", "error": "No module named 'tom<PERSON>'"}, {"procedure_path": "Calibration.py", "test_purpose": "Sample API for encoding, decoding JSON files'''\nimport json, time, os, glob, math, csv, statistics, ...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.2, "output": "", "error": null}, {"procedure_path": "Compression.py", "test_purpose": "CSV Columns", "test_methodology": "Standard test execution procedure", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: No module named 'visa'\n", "error": "No module named 'visa'"}, {"procedure_path": "CSVFile.py", "test_purpose": "auto_increment: to auto_increment output filnames", "test_methodology": "Standard test execution procedure", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "dme_burst.py", "test_purpose": "json encoder python\nDME_SEQ_TYPE Values\nX/Y receive channel\nRX attenuator control values", "test_methodology": "Standard test execution procedure", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step1a.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step1a_11-14-23.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step1b.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step1c.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step2a.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step2a_11-14-23.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step2b.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_10_Step3.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_12.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.57, "output": "EXECUTION_ERROR: MockATC.__init__() takes 1 positional argument but 2 were given\n", "error": "MockATC.__init__() takes 1 positional argument but 2 were given"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step1.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step3.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.18, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step4.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step5.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step6.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_1_step7.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_2_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_2_2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_2_2_11-14-23.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_1_old.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_2a.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_2a_old.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_2b.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_3_2b_old.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_4.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step1.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step3.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step4.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step5.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step6.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step7.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_5_Step8.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO181\\DO_181E_2_3_2_8.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_10.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Monday April 11 1:35:30 2020\n\n@author: E589493\n<PERSON><PERSON> QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: unexpected indent (N5172BSigGen.py, line 79)\n", "error": "unexpected indent (N5172BSigGen.py, line 79)"}, {"procedure_path": "DO189\\DO_189_2_2_12.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Wed April 28 3:20:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIFIC...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.18, "output": "EXECUTION_ERROR: unexpected indent (N5172BSigGen.py, line 79)\n", "error": "unexpected indent (N5172BSigGen.py, line 79)"}, {"procedure_path": "DO189\\DO_189_2_2_1_b.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Wed Feb 26 3:02:30 2020\n\n@author: E589493\n<PERSON><PERSON> QUALIFICAT...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_3.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Fri Mar 20 9:02:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIFICAT...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_4.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Tues March 3 3:02:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIFIC...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.18, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_6.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Tues March 3 3:20:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIFIC...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.18, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_7.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Tues April 28 21:20:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIF...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO189\\DO_189_2_2_8.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Tues April 27 3:20:30 2020\n\n@author: E589493\n<PERSON><PERSON>NS QUALIFI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: unexpected indent (N5172BSigGen.py, line 79)\n", "error": "unexpected indent (N5172BSigGen.py, line 79)"}, {"procedure_path": "DO189\\DO_189_DME_SG_Load.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Wed April 15 3:20:30 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUAL...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: unexpected indent (N5172BSigGen.py, line 79)\n", "error": "unexpected indent (N5172BSigGen.py, line 79)"}, {"procedure_path": "DO282\\DO282_248211.py", "test_purpose": "-*- coding: utf-8 -*-\n\nSCRIPT IDENTIFIER:  DO282_248211.py\n\nMODULE HISTORY:\n\nAUTHOR: E524495\n\nMODULE...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.19, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\DO282_248212.py", "test_purpose": "-*- coding: utf-8 -*-\n\nSCRIPT IDENTIFIER:  DO282_248212.py\n\nMODULE HISTORY:\n\nAUTHOR: H157797\n\nMODULE...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.18, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\DO282_248213.py", "test_purpose": "-*- coding: utf-8 -*-\n\nSCRIPT IDENTIFIER:  DO282_248213.py\n\nMODULE HISTORY:\n\nAUTHOR: H157797\n\nMODULE...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.19, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\DO282_24822.py", "test_purpose": "-*- coding: utf-8 -*-\n\nSCRIPT IDENTIFIER:  DO282_24822.py\n\nMODULE HISTORY:\n\nAUTHOR: H157797\n\nMODULE ...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.19, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\DO282_24823.py", "test_purpose": "-*- coding: utf-8 -*-\n\nSCRIPT IDENTIFIER:  DO282_24823.py\n\nMODULE HISTORY:\n\nAUTHOR: H157797\n\nMODULE ...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.19, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\FEC.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCopyright:   All source code, and data contained in this document is\nPropriet...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: No module named 'reedsolo'\n", "error": "No module named 're<PERSON><PERSON>'"}, {"procedure_path": "DO282\\UAT_CONNECTION.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCopyright:   All source code, and data contained in this document is\nPropriet...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.14, "output": "", "error": "  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\temp_wrapper_UAT_CONNECTION.py\", line 2\n    \"\"\"\n    ^^^\nSyntaxError: (unicode error) 'unicodeescape' codec can't decode bytes in position 51-53: truncated \\UXXXXXXXX escape\n"}, {"procedure_path": "DO282\\UAT_LOGGING.py", "test_purpose": "Mock UAT_LOGGING module for DO282 procedures\nProvides logging functionality for UAT test procedures", "test_methodology": "Standard test execution procedure", "status": "FAILED", "execution_time": 0.14, "output": "", "error": "  File \"C:\\DevForTXD\\FormalQual_TXD\\ProcedureQual\\temp_wrapper_UAT_LOGGING.py\", line 2\n    \"\"\"\n    ^^^\nSyntaxError: (unicode error) 'unicodeescape' codec can't decode bytes in position 51-53: truncated \\UXXXXXXXX escape\n"}, {"procedure_path": "DO385\\DO385_2_2_3_3.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on 3/25/2020\n\n@author: H118396\n<PERSON>\nCNS QUALIFICATION TE...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "DO385\\DO385_2_2_3_5.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on 3/25/2020\n\n@author: H118396\n<PERSON>\nCNS QUALIFICATION TE...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_3_8.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_4_1_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on Wed Feb 26 3:02:30 2020\n\n@author: H403316\n<PERSON><PERSON>\nCNS QUALIFICATI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_4_2_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_4_2_2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_5_4_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on 3/25/2020\n\n@author: H118396\n<PERSON>\nCNS QUALIFICATION TE...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.14, "output": "", "error": null}, {"procedure_path": "DO385\\DO385_2_2_4_6_2_1_2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_6_2_2_2.py", "test_purpose": "-*- coding: utf-8 -*-\n\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDiscription:\nRe...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_2_4_6_4_2.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on 5/5/2021\n\n@author: H118396\nMRSrebnicki\nCNS QUALIFICATION TEST GROU...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "DO385\\DO385_2_3_3_1.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on 3/25/2020\n\n@author: H118396\n<PERSON>\nCNS QUALIFICATION TE...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_A_Frequency.py", "test_purpose": "Created on <PERSON><PERSON> Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDi...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: invalid character '»' (U+00BB) (<string>, line 1)\n", "error": "invalid character '»' (U+00BB) (<string>, line 1)"}, {"procedure_path": "FAR43\\FAR43_B_Supression.py", "test_purpose": "Created on <PERSON><PERSON> Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDi...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: invalid character '»' (U+00BB) (<string>, line 1)\n", "error": "invalid character '»' (U+00BB) (<string>, line 1)"}, {"procedure_path": "FAR43\\FAR43_C_Sensitivity.py", "test_purpose": "Created on <PERSON><PERSON> Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALIFICATION TEST GROUP\n\nDi...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.15, "output": "EXECUTION_ERROR: invalid character '»' (U+00BB) (<string>, line 1)\n", "error": "invalid character '»' (U+00BB) (<string>, line 1)"}, {"procedure_path": "FAR43\\FAR43_D_Power.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_E_Diversity.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_F_ModeSAddress.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  5 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.16, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_G_ModeSFormat.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  6 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_H_ModeSAllCall.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  6 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_I_ATCRBSOnly.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  6 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "FAR43\\FAR43_J_Squitter.py", "test_purpose": "-*- coding: utf-8 -*-\n\nCreated on <PERSON>e Jan  6 08:58:20 2021\n\n@author: E282068\n<PERSON><PERSON><PERSON><PERSON>\nCNS QUALI...", "test_methodology": "See procedure file for detailed methodology", "status": "FAILED", "execution_time": 0.17, "output": "EXECUTION_ERROR: 'module' object is not callable\n", "error": "'module' object is not callable"}, {"procedure_path": "PulseTiming.py", "test_purpose": "Setup Scope for TCAS pulse measurements'''\naterm.logMessage(1, \"Procedure Started\")\n\naterm.instrumen...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "sensitivity.py", "test_purpose": "This program is intended to test receiver sensitivity for TCAS.\n\nOnce the RF_Card is initialized by ...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.19, "output": "", "error": null}, {"procedure_path": "Spectrum.py", "test_purpose": "Return power at specified center frequency on spectrum analyzer'''\naterm.logMessage(1, \"Procedure St...", "test_methodology": "See procedure file for detailed methodology", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}, {"procedure_path": "SPIDevices.py", "test_purpose": "Collection of functions to initialize and control\nthe SPI devices on the TXD RF Board.", "test_methodology": "Standard test execution procedure", "status": "PASSED", "execution_time": 0.23, "output": "", "error": null}, {"procedure_path": "txd_power.py", "test_purpose": "General test procedure: txd_power", "test_methodology": "Standard test execution procedure", "status": "PASSED", "execution_time": 0.16, "output": "", "error": null}], "coverage_analysis": {"by_standard": {"General": {"total": 11, "passed": 9}, "DO181": {"total": 35, "passed": 0}, "DO189": {"total": 9, "passed": 0}, "DO282": {"total": 8, "passed": 0}, "DO385": {"total": 11, "passed": 2}, "FAR43": {"total": 10, "passed": 0}}, "by_status": {"PASSED": 11, "FAILED": 73}, "by_execution_time": {"fast": 84, "medium": 0, "slow": 0}}, "mock_interface_validation": {"interfaces_required": ["ate_rm", "ATC5000NG", "ARINC_Client", "D3054Scope", "B4500CPwrMeter", "N9010BSpecAn", "N5172BSigGen", "RFBOB", "audio_processing"], "import_redirection": "TXDLib.Handlers -> MockHandlers", "compatibility_mode": "Full hardware compatibility maintained"}}