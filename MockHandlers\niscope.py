"""
Mock niscope module for TXD testing
"""

# Mock constants
NISCOPE_VAL_RISING = 1
NISCOPE_VAL_FALLING = 0
NISCOPE_VAL_DC = 2
NISCOPE_VAL_AC = 3

class MockSession:
    """Mock NI-SCOPE session"""
    
    def __init__(self, resource_name):
        self.resource_name = resource_name
        
    def configure_vertical(self, channel, range, coupling, probe_attenuation, enabled):
        pass
    
    def configure_horizontal_timing(self, min_sample_rate, min_num_pts, ref_position, num_records, enforce_realtime):
        pass
    
    def configure_trigger_immediate(self):
        pass
    
    def configure_trigger_edge(self, trigger_source, level, slope, trigger_coupling, holdoff, delay):
        pass
    
    def initiate(self):
        pass
    
    def fetch(self, channel, timeout):
        # Return mock waveform data
        import numpy as np
        return np.random.random(1000), None
    
    def close(self):
        pass
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

def Session(resource_name):
    """Create mock session"""
    return MockSession(resource_name)
