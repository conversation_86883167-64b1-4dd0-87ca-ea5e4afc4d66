"""
Mock toml module for TXD testing
"""

def load(file_path):
    """Mock TOML file loading"""
    return {
        "test_config": {
            "enabled": True,
            "timeout": 30,
            "retries": 3
        },
        "hardware": {
            "atc": "mock",
            "scope": "mock",
            "power_meter": "mock"
        }
    }

def loads(toml_string):
    """Mock TOML string parsing"""
    return {
        "parsed": True,
        "content": toml_string[:100] if len(toml_string) > 100 else toml_string
    }

def dump(data, file_handle):
    """Mock TOML file writing"""
    file_handle.write(f"# Mock TOML output\n[mock]\ndata = {data}\n")

def dumps(data):
    """Mock TOML string generation"""
    return f"# Mock TOML output\n[mock]\ndata = {data}\n"
