"""
Mock ftd2xx module for TXD testing
"""

class MockDevice:
    """Mock FTDI device"""
    
    def __init__(self):
        self.is_open = False
    
    def open(self, device_number=0):
        self.is_open = True
        return self
    
    def close(self):
        self.is_open = False
    
    def write(self, data):
        return len(data) if isinstance(data, (bytes, bytearray)) else len(str(data).encode())
    
    def read(self, bytes_to_read):
        return b'\x00' * bytes_to_read
    
    def setBaudRate(self, baud_rate):
        pass
    
    def setDataCharacteristics(self, word_length, stop_bits, parity):
        pass
    
    def setFlowControl(self, flow_control, xon, xoff):
        pass
    
    def setTimeouts(self, read_timeout, write_timeout):
        pass
    
    def purge(self, mask=None):
        pass
    
    def getStatus(self):
        return (0, 0, 0)  # rx_bytes, tx_bytes, event_status

def open(device_number=0):
    """Open mock FTDI device"""
    device = MockDevice()
    device.open(device_number)
    return device

def listDevices():
    """List mock devices"""
    return [b'Mock FTDI Device']

def getDeviceInfoDetail(device_number=0):
    """Get mock device info"""
    return {
        'serial': b'MOCK123',
        'description': b'Mock FTDI Device',
        'type': 6
    }
