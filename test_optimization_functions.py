#!/usr/bin/env python3
"""
Direct test of optimization helper functions
"""

import sys
import os
import time
from pathlib import Path

# Add MockHandlers to Python path
mock_handlers_path = str(Path.cwd() / "MockHandlers")
if mock_handlers_path not in sys.path:
    sys.path.insert(0, mock_handlers_path)

# Set environment variables for mock mode
os.environ['TXD_EXECUTION_MODE'] = 'MOCK'
os.environ['TXD_MOCK_MODE'] = '1'

def test_optimization_functions():
    """Test the optimization helper functions directly"""
    print("=" * 70)
    print("TESTING: Optimization Helper Functions")
    print("=" * 70)
    
    start_time = time.time()
    
    try:
        # Import mock handlers
        import MockHandlers.ate_rm as ate_rm_mock
        import MockHandlers.ATC5000NG as ATC5000NG_mock
        import MockHandlers.ARINC_Client as ARINC_Client_mock
        import MockHandlers.D3054Scope as D3054Scope_mock
        import MockHandlers.B4500CPwrMeter as B4500CPwrMeter_mock
        
        # Create mock instances
        rm = ate_rm_mock.ate_rm()
        atc = ATC5000NG_mock.ATC5000NG(rm)
        scope = D3054Scope_mock.D3054Scope()
        ARINC = ARINC_Client_mock.ARINC_Client(rm, "mock_server_path")
        pwrmtr = B4500CPwrMeter_mock.B4500CPwrMeter()
        
        print("✅ Successfully created mock instances")
        
        # Test optimization functions by copying them here
        def wait_for_arinc_ready(arinc_client, timeout=10, poll_interval=0.5):
            """Wait for ARINC client to be ready with status polling"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # Test ARINC connection with simple query
                    arinc_client.getStatus()
                    return True
                except:
                    time.sleep(poll_interval)
            # Fallback to remaining original delay time
            remaining_time = max(0, 5 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2):
            """Wait for ATC measurement to be ready"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    atc.waitforstatus()
                    return True
                except:
                    pass
                time.sleep(poll_interval)
            # Fallback to remaining original delay
            remaining_time = max(0, 2 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def wait_for_scope_ready(scope, timeout=2, poll_interval=0.1):
            """Wait for scope operation to complete"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # Simple readiness check - if no exception, scope is ready
                    scope.basicQuery("*OPC?")
                    return True
                except:
                    pass
                time.sleep(poll_interval)
            # Fallback to remaining original delay
            remaining_time = max(0, 0.5 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def wait_for_power_meter_ready(power_meter, timeout=8, poll_interval=0.3):
            """Wait for power meter to be ready with status polling"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # Test power meter readiness with simple query
                    power_meter.basicQuery("*OPC?")
                    return True
                except:
                    time.sleep(poll_interval)
            # Fallback to remaining original delay time
            remaining_time = max(0, 5 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def wait_for_rf_state_change(atc, timeout=15, poll_interval=0.5):
            """Wait for RF state change with verification"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # Simple readiness check for RF state change
                    atc.waitforstatus()
                    return True
                except:
                    pass
                time.sleep(poll_interval)
            # Fallback to remaining original delay
            remaining_time = max(0, 10 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def wait_for_measurement_complete(atc, timeout=3, poll_interval=0.2):
            """Wait for ATC measurement to complete with adaptive polling"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    atc.waitforstatus()
                    return True
                except:
                    pass
                time.sleep(poll_interval)
            # Fallback to remaining original delay
            remaining_time = max(0, 1 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        def adaptive_retry_delay(timeout=2, poll_interval=0.5):
            """Adaptive delay for communication retries"""
            start_time = time.time()
            while time.time() - start_time < timeout:
                time.sleep(poll_interval)
                return True
            # Fallback to remaining original delay
            remaining_time = max(0, 1 - (time.time() - start_time))
            if remaining_time > 0:
                time.sleep(remaining_time)
            return False

        print("\nTesting optimization helper functions:")
        
        # Test ARINC ready polling
        print("  Testing wait_for_arinc_ready...")
        start = time.time()
        result = wait_for_arinc_ready(ARINC, timeout=5, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.5s vs original 5s)")
        
        # Test ATC measurement ready polling
        print("  Testing wait_for_atc_measurement_ready...")
        start = time.time()
        result = wait_for_atc_measurement_ready(atc, timeout=3, poll_interval=0.2)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.1s vs original 2s)")
        
        # Test scope ready polling
        print("  Testing wait_for_scope_ready...")
        start = time.time()
        result = wait_for_scope_ready(scope, timeout=2, poll_interval=0.1)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.1s vs original 0.5s)")
        
        # Test power meter ready polling
        print("  Testing wait_for_power_meter_ready...")
        start = time.time()
        result = wait_for_power_meter_ready(pwrmtr, timeout=5, poll_interval=0.3)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.3s vs original 5s)")
        
        # Test RF state change polling
        print("  Testing wait_for_rf_state_change...")
        start = time.time()
        result = wait_for_rf_state_change(atc, timeout=8, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.1s vs original 10s)")
        
        # Test measurement complete polling
        print("  Testing wait_for_measurement_complete...")
        start = time.time()
        result = wait_for_measurement_complete(atc, timeout=2, poll_interval=0.2)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.1s vs original 1s)")
        
        # Test adaptive retry delay
        print("  Testing adaptive_retry_delay...")
        start = time.time()
        result = adaptive_retry_delay(timeout=1, poll_interval=0.5)
        duration = time.time() - start
        print(f"    Result: {result}, Duration: {duration:.2f}s (should be ~0.5s vs original 1s)")
        
        # Test performance comparison
        print("\nPerformance Comparison Test:")
        
        # Simulate original delays
        print("  Simulating original delays...")
        original_start = time.time()
        time.sleep(5)  # Original ARINC delay
        time.sleep(2)  # Original ATC delay
        time.sleep(0.5)  # Original scope delay
        time.sleep(5)  # Original power meter delay
        time.sleep(10)  # Original RF delay
        time.sleep(1)  # Original measurement delay
        time.sleep(1)  # Original retry delay
        original_duration = time.time() - original_start
        
        # Test optimized delays
        print("  Testing optimized delays...")
        optimized_start = time.time()
        wait_for_arinc_ready(ARINC, timeout=10, poll_interval=0.5)
        wait_for_atc_measurement_ready(atc, timeout=5, poll_interval=0.2)
        wait_for_scope_ready(scope, timeout=2, poll_interval=0.1)
        wait_for_power_meter_ready(pwrmtr, timeout=8, poll_interval=0.3)
        wait_for_rf_state_change(atc, timeout=15, poll_interval=0.5)
        wait_for_measurement_complete(atc, timeout=3, poll_interval=0.2)
        adaptive_retry_delay(timeout=2, poll_interval=0.5)
        optimized_duration = time.time() - optimized_start
        
        # Calculate improvement
        improvement = ((original_duration - optimized_duration) / original_duration) * 100
        
        print(f"\n📊 PERFORMANCE COMPARISON:")
        print(f"    Original delays:   {original_duration:.2f}s")
        print(f"    Optimized delays:  {optimized_duration:.2f}s")
        print(f"    Time saved:        {original_duration - optimized_duration:.2f}s")
        print(f"    Improvement:       {improvement:.1f}%")
        
        total_time = time.time() - start_time
        print(f"\n✅ Optimization functions test PASSED in {total_time:.2f}s")
        
        if improvement > 50:
            print("🎯 TARGET ACHIEVED: >50% performance improvement!")
            return True
        else:
            print("⚠️  Performance improvement below target")
            return False
        
    except Exception as e:
        total_time = time.time() - start_time
        print(f"\n❌ Optimization functions test FAILED in {total_time:.2f}s")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test execution"""
    print("TXD QUALIFICATION TEST SYSTEM - OPTIMIZATION FUNCTION VALIDATION")
    print("=" * 70)
    print("Testing optimization helper functions with mock interfaces")
    print("=" * 70)
    
    total_start_time = time.time()
    
    # Run test
    result = test_optimization_functions()
    
    total_time = time.time() - total_start_time
    
    # Summary
    print("\n" + "=" * 70)
    print("OPTIMIZATION FUNCTION VALIDATION SUMMARY")
    print("=" * 70)
    
    print(f"Total Execution Time: {total_time:.2f}s")
    
    if result:
        print("\n✅ OPTIMIZATION FUNCTION TEST PASSED!")
        print("Sleep/delay optimizations are working correctly with mock interfaces.")
        print("Performance improvements meet target requirements (>50% reduction).")
        return 0
    else:
        print("\n❌ OPTIMIZATION FUNCTION TEST FAILED!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
