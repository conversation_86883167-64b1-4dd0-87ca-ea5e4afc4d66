"""
Mock pyvisa module for TXD testing
"""

class MockResource:
    """Mock VISA resource"""
    
    def __init__(self, resource_name):
        self.resource_name = resource_name
        self.timeout = 5000
        
    def write(self, command):
        """Mock write command"""
        return len(command)
    
    def read(self):
        """Mock read response"""
        return "OK"
    
    def query(self, command):
        """Mock query command"""
        if "IDN" in command.upper():
            return "Mock Instrument,Model123,SN456,FW1.0"
        elif "STATUS" in command.upper():
            return "0"
        elif "OPC" in command.upper():
            return "1"
        else:
            return "0"
    
    def close(self):
        """Close resource"""
        pass
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

class MockResourceManager:
    """Mock VISA resource manager"""
    
    def __init__(self):
        pass
    
    def open_resource(self, resource_name, **kwargs):
        """Open mock resource"""
        return MockResource(resource_name)
    
    def list_resources(self):
        """List mock resources"""
        return ['TCPIP::*************::INSTR', 'USB0::0x1234::0x5678::SN123::INSTR']
    
    def close(self):
        """Close resource manager"""
        pass

def ResourceManager():
    """Create mock resource manager"""
    return MockResourceManager()

# Constants
constants = type('Constants', (), {
    'VI_SUCCESS': 0,
    'VI_ERROR_TIMEOUT': -1073807339,
    'VI_ERROR_RESOURCE_NFOUND': -1073807343
})()
