#!/usr/bin/env python3
"""
Test the fixed DO181 sequence to confirm it's still working
"""

import subprocess
import sys
import os
from pathlib import Path

# Set up environment like sequence_runner.py
env = os.environ.copy()
env['TXD_EXECUTION_MODE'] = 'MOCK'

current_dir = Path.cwd()
python_path_parts = [str(current_dir)]

# For mock mode, prioritize MockHandlers in the Python path
mock_handlers_path = current_dir / "MockHandlers"
python_path_parts.insert(0, str(mock_handlers_path))
env['TXD_MOCK_MODE'] = '1'

if 'PYTHONPATH' in env:
    env['PYTHONPATH'] = f"{os.pathsep.join(python_path_parts)}{os.pathsep}{env['PYTHONPATH']}"
else:
    env['PYTHONPATH'] = os.pathsep.join(python_path_parts)

# Test the previously failing sequence that we fixed
sequence = "TXDLib/Procedures/DO181/DO_181E_2_3_2_8.py"

print("Testing fixed DO181 sequence...")
print("=" * 60)
print(f"Sequence: {sequence}")
print("Expected: PASSED (data_log_start/stop methods added)")

try:
    result = subprocess.run(
        [sys.executable, sequence],
        capture_output=True,
        text=True,
        timeout=300,  # 5 minute timeout
        env=env
    )
    
    print(f"\nReturn code: {result.returncode}")
    if result.returncode == 0:
        print("✅ PASSED - Sequence still working correctly!")
        print("\nKey output:")
        # Show last few lines of output
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            for line in lines[-5:]:
                if line.strip():
                    print(f"  {line}")
    else:
        print("❌ FAILED - Regression detected!")
        print("STDERR:")
        print(result.stderr[-500:])  # Last 500 chars of error
        
except subprocess.TimeoutExpired:
    print("⏱️ TIMEOUT - Sequence taking too long (likely still working)")
except Exception as e:
    print(f"❌ EXCEPTION: {e}")

print("\n" + "=" * 60)
print("Fixed sequence validation complete!")
